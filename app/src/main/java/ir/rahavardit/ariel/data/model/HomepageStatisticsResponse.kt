package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the new homepage statistics response from the API.
 */
data class HomepageStatisticsResponse(
    @SerializedName("years")
    val years: List<Int>,

    @SerializedName("names_of_months__persian")
    val namesOfMonthsPersian: List<String>,

    @SerializedName("chosenyear")
    val chosenYear: Int,

    @SerializedName("chosenmonthstart")
    val chosenMonthStart: Int,

    @SerializedName("chosenmonthend")
    val chosenMonthEnd: Int,

    @SerializedName("chosenmonthstart__zeroed")
    val chosenMonthStartZeroed: String,

    @SerializedName("chosenmonthend__zeroed")
    val chosenMonthEndZeroed: String,

    @SerializedName("chosenyear__persian")
    val chosenYearPersian: String,

    @SerializedName("chosenmonthstart__zeroed__persian")
    val chosenMonthStartZeroedPersian: String,

    @SerializedName("chosenmonthend__zeroed__persian")
    val chosenMonthEndZeroedPersian: String,

    @SerializedName("income_objects")
    val incomeObjects: List<IncomeObject>,

    @SerializedName("expenditure_objects")
    val expenditureObjects: List<ExpenditureObject>,

    @SerializedName("event_objects")
    val eventObjects: List<EventObject>
)

/**
 * Data class representing an income object.
 */
data class IncomeObject(
    @SerializedName("id")
    val id: Int,

    @SerializedName("mode")
    val mode: String,

    @SerializedName("title")
    val title: String,

    @SerializedName("author")
    val author: Author,

    @SerializedName("amount")
    val amount: Int,

    @SerializedName("year")
    val year: Int,

    @SerializedName("month")
    val month: Int,

    @SerializedName("day")
    val day: Int,

    @SerializedName("bank")
    val bank: Bank,

    @SerializedName("category")
    val category: FinancialCategory,

    @SerializedName("tags")
    val tags: List<Int>,

    @SerializedName("tags_names")
    val tagsNames: List<String>,

    @SerializedName("short_uuid")
    val shortUuid: String,

    @SerializedName("active")
    val active: Boolean,

    @SerializedName("created")
    val created: String,

    @SerializedName("updated")
    val updated: String
)

/**
 * Data class representing an expenditure object.
 */
data class ExpenditureObject(
    @SerializedName("id")
    val id: Int,

    @SerializedName("mode")
    val mode: String,

    @SerializedName("title")
    val title: String,

    @SerializedName("author")
    val author: Author,

    @SerializedName("amount")
    val amount: Int,

    @SerializedName("year")
    val year: Int,

    @SerializedName("month")
    val month: Int,

    @SerializedName("day")
    val day: Int,

    @SerializedName("bank")
    val bank: Bank,

    @SerializedName("category")
    val category: FinancialCategory,

    @SerializedName("tags")
    val tags: List<Int>,

    @SerializedName("tags_names")
    val tagsNames: List<String>,

    @SerializedName("short_uuid")
    val shortUuid: String,

    @SerializedName("active")
    val active: Boolean,

    @SerializedName("created")
    val created: String,

    @SerializedName("updated")
    val updated: String
)

/**
 * Data class representing an event object.
 */
data class EventObject(
    @SerializedName("id")
    val id: Int,

    @SerializedName("title")
    val title: String,

    @SerializedName("author")
    val author: Author,

    @SerializedName("year")
    val year: Int,

    @SerializedName("month")
    val month: Int,

    @SerializedName("day")
    val day: Int,

    @SerializedName("active")
    val active: Boolean,

    @SerializedName("short_uuid")
    val shortUuid: String,

    @SerializedName("created")
    val created: String,

    @SerializedName("updated")
    val updated: String
)



/**
 * Data class representing a bank.
 */
data class Bank(
    @SerializedName("id")
    val id: Int,

    @SerializedName("title")
    val title: String,

    @SerializedName("short_uuid")
    val shortUuid: String
)

/**
 * Data class representing a category for income/expenditure.
 */
data class FinancialCategory(
    @SerializedName("id")
    val id: Int,

    @SerializedName("title")
    val title: String,

    @SerializedName("short_uuid")
    val shortUuid: String
)
