package ir.rahavardit.ariel.ui.home

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import ir.rahavardit.ariel.data.model.IncomeObject
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.data.model.EventObject

class HomepageTabsAdapter(private val fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {

    private var incomeData: List<IncomeObject> = emptyList()
    private var expenditureData: List<ExpenditureObject> = emptyList()
    private var eventData: List<EventObject> = emptyList()

    private var incomeFragment: IncomeTabFragment? = null
    private var expenditureFragment: ExpenditureTabFragment? = null
    private var eventFragment: EventTabFragment? = null

    override fun getItemCount(): Int = 3

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> {
                incomeFragment = IncomeTabFragment.newInstance()
                incomeFragment!!.updateData(incomeData)
                incomeFragment!!
            }
            1 -> {
                expenditureFragment = ExpenditureTabFragment.newInstance()
                expenditureFragment!!.updateData(expenditureData)
                expenditureFragment!!
            }
            2 -> {
                eventFragment = EventTabFragment.newInstance()
                eventFragment!!.updateData(eventData)
                eventFragment!!
            }
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }

    fun updateIncomeData(data: List<IncomeObject>) {
        incomeData = data
        incomeFragment?.updateData(data)
    }

    fun updateExpenditureData(data: List<ExpenditureObject>) {
        expenditureData = data
        expenditureFragment?.updateData(data)
    }

    fun updateEventData(data: List<EventObject>) {
        eventData = data
        eventFragment?.updateData(data)
    }
}
