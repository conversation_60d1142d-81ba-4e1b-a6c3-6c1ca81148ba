package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.tabs.TabLayoutMediator
import ir.rahavardit.ariel.ArielApplication
import ir.rahavardit.ariel.databinding.FragmentHomeBinding
import ir.rahavardit.ariel.utils.PersianUtils

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var tabsAdapter: HomepageTabsAdapter

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupTabs()
        setupSpinners()
        setupObservers()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageStatistics()
    }

    private fun setupTabs() {
        tabsAdapter = HomepageTabsAdapter(requireActivity())
        binding.viewPager.adapter = tabsAdapter

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "درآمد"
                1 -> "هزینه"
                2 -> "رویداد"
                else -> ""
            }
        }.attach()
    }

    private fun setupSpinners() {
        // Initially empty - will be populated when data is loaded
        setupYearSpinner(emptyList(), 0)
        setupMonthSpinners(emptyList(), 0, 0)
    }

    private fun setupObservers() {
        homeViewModel.homepageStatistics.observe(viewLifecycleOwner) { statistics ->
            updateHomepageData(statistics)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (!isLoading) {
                binding.errorMessage.visibility = View.GONE
            }
        }
    }

    private fun updateHomepageData(statistics: ir.rahavardit.ariel.data.model.HomepageStatisticsResponse) {
        // Update spinners
        setupYearSpinner(statistics.years, statistics.chosenYear)
        setupMonthSpinners(statistics.namesOfMonthsPersian, statistics.chosenMonthStart, statistics.chosenMonthEnd)

        // Update tab data through adapter
        tabsAdapter.updateIncomeData(statistics.incomeObjects)
        tabsAdapter.updateExpenditureData(statistics.expenditureObjects)
        tabsAdapter.updateEventData(statistics.eventObjects)

        // Hide error message
        binding.errorMessage.visibility = View.GONE
    }

    private fun setupYearSpinner(years: List<Int>, selectedYear: Int) {
        val persianYears = years.map { PersianUtils.convertToPersianDigits(it.toString()) }
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, persianYears)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        binding.spinnerYear.adapter = adapter

        // Set selected item
        val selectedIndex = years.indexOf(selectedYear)
        if (selectedIndex >= 0) {
            binding.spinnerYear.setSelection(selectedIndex)
        }

        binding.spinnerYear.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (years.isNotEmpty() && position < years.size) {
                    val selectedYearValue = years[position]
                    homeViewModel.updateFilters(selectedYearValue, null, null)
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }

    private fun setupMonthSpinners(months: List<String>, selectedMonthStart: Int, selectedMonthEnd: Int) {
        // Add "همه" (All) option at the beginning
        val monthsWithAll = listOf("همه") + months
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, monthsWithAll)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        // Setup month start spinner
        binding.spinnerMonthStart.adapter = adapter
        val startIndex = if (selectedMonthStart == 0) 0 else selectedMonthStart
        binding.spinnerMonthStart.setSelection(startIndex)

        binding.spinnerMonthStart.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val monthValue = if (position == 0) 0 else position
                homeViewModel.updateFilters(null, monthValue, null)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // Setup month end spinner
        binding.spinnerMonthEnd.adapter = adapter
        val endIndex = if (selectedMonthEnd == 0) 0 else selectedMonthEnd
        binding.spinnerMonthEnd.setSelection(endIndex)

        binding.spinnerMonthEnd.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val monthValue = if (position == 0) 0 else position
                homeViewModel.updateFilters(null, null, monthValue)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }



    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
