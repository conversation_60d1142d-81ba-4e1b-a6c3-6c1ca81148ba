package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.data.model.EventObject
import ir.rahavardit.ariel.databinding.FragmentEventTabBinding

class EventTabFragment : Fragment() {

    private var _binding: FragmentEventTabBinding? = null
    private val binding get() = _binding!!
    private lateinit var eventAdapter: HomepageEventAdapter
    private var pendingData: List<EventObject>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentEventTabBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()

        // Apply pending data if available
        pendingData?.let { data ->
            updateDataInternal(data)
            pendingData = null
        }
    }

    private fun setupRecyclerView() {
        eventAdapter = HomepageEventAdapter()
        binding.recyclerEvents.apply {
            adapter = eventAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    fun updateData(eventObjects: List<EventObject>) {
        if (_binding == null) {
            // View not created yet, store data for later
            pendingData = eventObjects
        } else {
            updateDataInternal(eventObjects)
        }
    }

    private fun updateDataInternal(eventObjects: List<EventObject>) {
        if (eventObjects.isEmpty()) {
            binding.recyclerEvents.visibility = View.GONE
            binding.emptyMessage.visibility = View.VISIBLE
        } else {
            binding.recyclerEvents.visibility = View.VISIBLE
            binding.emptyMessage.visibility = View.GONE
            eventAdapter.submitList(eventObjects)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(): EventTabFragment {
            return EventTabFragment()
        }
    }
}
