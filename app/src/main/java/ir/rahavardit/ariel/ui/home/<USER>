package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.databinding.FragmentExpenditureTabBinding

class ExpenditureTabFragment : Fragment() {

    private var _binding: FragmentExpenditureTabBinding? = null
    private val binding get() = _binding!!
    private lateinit var expenditureAdapter: ExpenditureAdapter
    private var pendingData: List<ExpenditureObject>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentExpenditureTabBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()

        // Apply pending data if available
        pendingData?.let { data ->
            updateDataInternal(data)
            pendingData = null
        }
    }

    private fun setupRecyclerView() {
        expenditureAdapter = ExpenditureAdapter()
        binding.recyclerExpenditure.apply {
            adapter = expenditureAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    fun updateData(expenditureObjects: List<ExpenditureObject>) {
        if (_binding == null) {
            // View not created yet, store data for later
            pendingData = expenditureObjects
        } else {
            updateDataInternal(expenditureObjects)
        }
    }

    private fun updateDataInternal(expenditureObjects: List<ExpenditureObject>) {
        if (expenditureObjects.isEmpty()) {
            binding.recyclerExpenditure.visibility = View.GONE
            binding.emptyMessage.visibility = View.VISIBLE
        } else {
            binding.recyclerExpenditure.visibility = View.VISIBLE
            binding.emptyMessage.visibility = View.GONE
            expenditureAdapter.submitList(expenditureObjects)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(): ExpenditureTabFragment {
            return ExpenditureTabFragment()
        }
    }
}
