package ir.rahavardit.ariel.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.data.model.IncomeObject
import ir.rahavardit.ariel.databinding.FragmentIncomeTabBinding

class IncomeTabFragment : Fragment() {

    private var _binding: FragmentIncomeTabBinding? = null
    private val binding get() = _binding!!
    private lateinit var incomeAdapter: IncomeAdapter
    private var pendingData: List<IncomeObject>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentIncomeTabBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()

        // Apply pending data if available
        pendingData?.let { data ->
            updateDataInternal(data)
            pendingData = null
        }
    }

    private fun setupRecyclerView() {
        incomeAdapter = IncomeAdapter()
        binding.recyclerIncome.apply {
            adapter = incomeAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    fun updateData(incomeObjects: List<IncomeObject>) {
        if (_binding == null) {
            // View not created yet, store data for later
            pendingData = incomeObjects
        } else {
            updateDataInternal(incomeObjects)
        }
    }

    private fun updateDataInternal(incomeObjects: List<IncomeObject>) {
        if (incomeObjects.isEmpty()) {
            binding.recyclerIncome.visibility = View.GONE
            binding.emptyMessage.visibility = View.VISIBLE
        } else {
            binding.recyclerIncome.visibility = View.VISIBLE
            binding.emptyMessage.visibility = View.GONE
            incomeAdapter.submitList(incomeObjects)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(): IncomeTabFragment {
            return IncomeTabFragment()
        }
    }
}
